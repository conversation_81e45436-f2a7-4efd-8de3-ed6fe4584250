{"configureSettings": [{"id": 1, "code": "RPS1", "description": "robot rotate speed", "value": 1000, "unit": "step/second"}, {"id": 2, "code": "RPS2", "description": "robot extend speed", "value": 800, "unit": "step/second"}, {"id": 3, "code": "RPS3", "description": "robot up down speed", "value": 500, "unit": "step/second"}, {"id": 4, "code": "RPS4", "description": "robot move slowly", "value": 0, "unit": "N/A"}, {"id": 5, "code": "RPS5", "description": "T-axis move speed slowly", "value": 25, "unit": "precent"}, {"id": 6, "code": "RPS6", "description": "R-axis move speed slowly", "value": 25, "unit": "precent"}, {"id": 7, "code": "RPS7", "description": "Z-axis move speed slowly", "value": 25, "unit": "precent"}, {"id": 8, "code": "RPS8", "description": "robot rotate max time", "value": 30, "unit": "second"}, {"id": 9, "code": "RPS9", "description": "robot extend max time", "value": 30, "unit": "second"}, {"id": 10, "code": "RPS10", "description": "robot up down max time", "value": 30, "unit": "second"}, {"id": 11, "code": "RPS11", "description": "Deviation step for T-axis home", "value": 100, "unit": "step"}, {"id": 12, "code": "RPS12", "description": "robot home T-axis max time", "value": 60, "unit": "second"}, {"id": 13, "code": "RPS13", "description": "robot home R-axis max time", "value": 60, "unit": "second"}, {"id": 14, "code": "RPS14", "description": "robot home Z-axis max time", "value": 60, "unit": "second"}, {"id": 15, "code": "RPS15", "description": "T-axis deviation for R-axis zero", "value": 100, "unit": "step"}, {"id": 16, "code": "RPS16", "description": "Z-axis step deviation for feedback vacuum", "value": 100, "unit": "step"}, {"id": 17, "code": "RPS17", "description": "Z-axis step deviation for shuttle no vacuum", "value": 100, "unit": "step"}, {"id": 18, "code": "RPS18", "description": "zpin wafer 4/5", "value": 100, "unit": "step"}, {"id": 19, "code": "RPS19", "description": "zpin wafer 6", "value": 100, "unit": "step"}, {"id": 20, "code": "RPS20", "description": "zpin wafer 8", "value": 1230, "unit": "step"}, {"id": 21, "code": "RPS21", "description": "Z-axis get/put wafer delta cassette 4", "value": 100, "unit": "step"}, {"id": 22, "code": "RPS22", "description": "Z-axis get/put wafer delta cassette 6", "value": 100, "unit": "step"}, {"id": 23, "code": "RPS23", "description": "Z-axis get/put wafer delta cassette 8", "value": 600, "unit": "step"}, {"id": 24, "code": "RPS24", "description": "Z-axis get/put wafer delta cooling chamber", "value": 50, "unit": "step"}, {"id": 25, "code": "RPS25", "description": "max delta value for pin search", "value": 100, "unit": "step"}, {"id": 26, "code": "RPS26", "description": "wafer actually status check", "value": 1, "unit": "N/A"}, {"id": 27, "code": "RPS27", "description": "Z-axis height for robot rotation", "value": 15000, "unit": "step"}, {"id": 28, "code": "RPS28", "description": "lowest step for pin search", "value": 100, "unit": "step"}, {"id": 29, "code": "RPS29", "description": "chambre pressure review", "value": 1, "unit": "N/A"}]}