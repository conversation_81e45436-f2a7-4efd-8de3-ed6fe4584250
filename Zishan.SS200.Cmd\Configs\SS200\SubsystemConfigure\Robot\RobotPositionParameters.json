{"positionParameters": [{"AxisType": 1, "code": "RP1", "description": "T-axis smooth to CHA", "id": 1, "unit": "step", "value": 50100}, {"AxisType": 1, "code": "RP2", "description": "T-axis smooth to CHB", "id": 2, "unit": "step", "value": 25000}, {"AxisType": 1, "code": "RP3", "description": "T-axis smooth to cooling chamber", "id": 3, "unit": "step", "value": 75000}, {"AxisType": 1, "code": "RP4", "description": "T-axis smooth to cassette", "id": 4, "unit": "step", "value": 0}, {"AxisType": 1, "code": "RP5", "description": "T-axis nose to CHA", "id": 5, "unit": "step", "value": 50}, {"AxisType": 1, "code": "RP6", "description": "T-axis nose to CHB", "id": 6, "unit": "step", "value": 75000}, {"AxisType": 1, "code": "RP7", "description": "T-axis nose to cooling chamber", "id": 7, "unit": "step", "value": 25000}, {"AxisType": 1, "code": "RP8", "description": "T-axis nose to cassette", "id": 8, "unit": "step", "value": 50050}, {"AxisType": 1, "code": "RP9", "description": "T-axis zero", "id": 9, "unit": "step", "value": 11}, {"AxisType": 2, "code": "RP10", "description": "R-axis smooth extend and face to CHA", "id": 10, "unit": "step", "value": -6430}, {"AxisType": 2, "code": "RP11", "description": "R-axis smooth extend and face to CHB", "id": 11, "unit": "step", "value": -6450}, {"AxisType": 2, "code": "RP12", "description": "R-axis nose extend and face to CHA", "id": 12, "unit": "step", "value": 6450}, {"AxisType": 2, "code": "RP13", "description": "R-axis nose extend and face to CHB", "id": 13, "unit": "step", "value": 6458}, {"AxisType": 2, "code": "RP14", "description": "R-axis smooth face to cooling chamber and extend", "id": 14, "unit": "step", "value": -6458}, {"AxisType": 2, "code": "RP15", "description": "R-axis nose extend and face to cooling chamber", "id": 15, "unit": "step", "value": 6455}, {"AxisType": 2, "code": "RP16", "description": "R-axis smooth face to cassette and extend", "id": 16, "unit": "step", "value": -8333}, {"AxisType": 2, "code": "RP17", "description": "R-axis nose face to cassette and extend", "id": 17, "unit": "step", "value": 8333}, {"AxisType": 2, "code": "RP18", "description": "R-axis zero position", "id": 18, "unit": "step", "value": 22}, {"AxisType": 3, "code": "RP19", "description": "Z-axis height at smooth to CHA", "id": 19, "unit": "step", "value": 1500}, {"AxisType": 3, "code": "RP20", "description": "Z-axis height at smooth to CHB", "id": 20, "unit": "step", "value": 1600}, {"AxisType": 3, "code": "RP21", "description": "Z-axis height at smooth to CT", "id": 21, "unit": "step", "value": 1700}, {"AxisType": 3, "code": "RP22", "description": "Z-axis height at smooth to CB", "id": 22, "unit": "step", "value": 1800}, {"AxisType": 3, "code": "RP23", "description": "Z-axis height at nose to CHA", "id": 23, "unit": "step", "value": 1900}, {"AxisType": 3, "code": "RP24", "description": "Z-axis height at nose to CHB", "id": 24, "unit": "step", "value": 2000}, {"AxisType": 3, "code": "RP25", "description": "Z-axis height at nose to CT get", "id": 25, "unit": "step", "value": 2100}, {"AxisType": 3, "code": "RP26", "description": "Z-axis height at nose to CB get", "id": 26, "unit": "step", "value": 2200}, {"AxisType": 3, "code": "RP27", "description": "Z-axis zero position", "id": 27, "unit": "step", "value": 6666}, {"AxisType": 3, "code": "RP28", "description": "Z-axis height to pin search", "id": 28, "unit": "step", "value": 1000}]}